import 'package:flutter/material.dart';
import 'package:rideoon/services/package_category_service.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Test file to verify API integration for cargo/package management
class ApiIntegrationTest {
  
  /// Test package category service
  static Future<void> testPackageCategoryService() async {
    print('🧪 Testing Package Category Service...');
    
    try {
      // Test fetching categories
      final response = await PackageCategoryService.getPackageCategories();
      
      if (response.success && response.data != null) {
        print('✅ Categories fetched successfully');
        print('📦 Categories data: ${response.data}');
        
        // Test flattening categories
        final flattened = PackageCategoryService.getFlattenedCategories(response.data!);
        print('📋 Flattened categories: ${flattened.length} items');
        
        for (final category in flattened.take(5)) {
          print('   - ${category['name']}: ${category['hierarchy']}');
        }
        
        // Test category hierarchy lookup
        if (flattened.isNotEmpty) {
          final testCategory = flattened.first['name'] as String;
          final hierarchy = PackageCategoryService.getCategoryHierarchy(testCategory, response.data!);
          print('🔍 Hierarchy for "$testCategory": $hierarchy');
        }
        
      } else {
        print('❌ Failed to fetch categories: ${response.message}');
        
        // Test default categories
        final defaultCategories = PackageCategoryService.getDefaultCategories();
        print('📦 Using default categories: ${defaultCategories.keys.toList()}');
      }
      
    } catch (e) {
      print('❌ Exception in category service test: $e');
    }
  }
  
  /// Test CargoItem API format conversion
  static void testCargoItemApiFormat() {
    print('\n🧪 Testing CargoItem API Format Conversion...');
    
    try {
      // Create test cargo item
      final testItem = CargoItem(
        itemName: 'iPhone 15 Pro',
        description: 'Latest iPhone model with advanced features',
        category: 'Electronics',
        itemType: 'Mobile Phone',
        weight: 0.2,
        quantity: 1,
        durability: ItemDurability.fragile,
        value: 150000.0,
        imagePaths: ['image1.jpg', 'image2.jpg'],
      );
      
      // Test API format conversion
      final apiFormat = testItem.toApiPackageFormat(
        label: 'iPhone delivery to customer',
        categoryHierarchy: ['Gadget', 'Mobile phones & accessories'],
        packagePickupType: 'instant',
        pickupAddress: {
          'name': 'John Doe',
          'phoneNumber': 2348012345678,
          'street': '123 Main Street',
          'city': 'Lagos',
          'state': 'Lagos',
          'country': 'Nigeria',
          'longitude': 3.3792,
          'latitude': 6.5244,
        },
        deliveryAddress: {
          'name': 'Jane Smith',
          'phoneNumber': 2348087654321,
          'street': '456 Oak Avenue',
          'city': 'Abuja',
          'state': 'FCT',
          'country': 'Nigeria',
          'longitude': 7.4951,
          'latitude': 9.0765,
        },
      );
      
      print('✅ API format conversion successful');
      print('📦 Package label: ${apiFormat['label']}');
      print('📦 Category hierarchy: ${apiFormat['category']}');
      print('📦 Weight format: ${apiFormat['weight']}');
      print('📦 Fragile flag: ${apiFormat['fragile']}');
      print('📦 Pickup type: ${apiFormat['packagePickupType']}');
      print('📦 Meta data: ${apiFormat['meta']}');
      
      // Test legacy format
      final legacyFormat = testItem.toLegacyFormat();
      print('📦 Legacy format: $legacyFormat');
      
    } catch (e) {
      print('❌ Exception in cargo item test: $e');
    }
  }
  
  /// Test order data structure compatibility
  static void testOrderDataCompatibility() {
    print('\n🧪 Testing Order Data Compatibility...');
    
    try {
      // Test old format order
      final oldFormatOrder = {
        'trackingNumber': '#RO12345',
        'status': 'pending',
        'cargoItems': [
          {
            'itemName': 'Laptop',
            'description': 'Gaming laptop',
            'category': 'Electronics',
            'itemType': 'Computer',
            'weight': 2.5,
            'quantity': 1,
            'durability': 'fragile',
            'value': 500000.0,
            'imagePaths': ['laptop1.jpg'],
          }
        ],
      };
      
      // Test new API format order
      final newFormatOrder = {
        'trackingNumber': '#RO67890',
        'status': 'paid',
        'packages': [
          {
            'label': 'Laptop delivery',
            'fragile': true,
            'quantity': 1,
            'category': ['Gadget', 'Laptops & small gadgets', 'Laptops'],
            'packagePickupType': 'instant',
            'weight': {
              'unit': 'KG',
              'value': 2.5,
            },
            'meta': {
              'itemName': 'Gaming Laptop',
              'description': 'High-performance gaming laptop',
              'itemType': 'Computer',
              'durability': 'Fragile',
              'value': 500000.0,
              'imagePaths': ['laptop1.jpg', 'laptop2.jpg'],
            },
          }
        ],
      };
      
      print('✅ Order format compatibility test completed');
      print('📦 Old format items: ${oldFormatOrder['cargoItems']?.length ?? 0}');
      print('📦 New format packages: ${newFormatOrder['packages']?.length ?? 0}');
      
    } catch (e) {
      print('❌ Exception in order compatibility test: $e');
    }
  }
  
  /// Run all integration tests
  static Future<void> runAllTests() async {
    print('🚀 Starting API Integration Tests...\n');
    
    await testPackageCategoryService();
    testCargoItemApiFormat();
    testOrderDataCompatibility();
    
    print('\n✅ All integration tests completed!');
  }
  
  /// Test widget for manual testing in the app
  static Widget buildTestWidget(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('API Integration Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'API Integration Tests',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () async {
                await testPackageCategoryService();
                Toast.success('Category service test completed');
              },
              child: Text('Test Package Categories'),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                testCargoItemApiFormat();
                Toast.success('CargoItem format test completed');
              },
              child: Text('Test CargoItem API Format'),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                testOrderDataCompatibility();
                Toast.success('Order compatibility test completed');
              },
              child: Text('Test Order Compatibility'),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () async {
                await runAllTests();
                Toast.success('All tests completed');
              },
              child: Text('Run All Tests'),
            ),
            
            SizedBox(height: 20),
            
            Text(
              'Check console output for detailed test results.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
